{"private": true, "type": "module", "scripts": {"build": "vite build", "dev": "vite"}, "devDependencies": {"@headlessui/react": "^2.0.0", "@inertiajs/react": "^2.0.0", "@tailwindcss/forms": "^0.5.2", "@types/react-dom": "^19.0.3", "@vitejs/plugin-react": "^4.2.0", "alpinejs": "^3.4.2", "autoprefixer": "^10.4.2", "axios": "^1.7.4", "concurrently": "^9.0.1", "laravel-vite-plugin": "^1.2.0", "postcss": "^8.4.31", "react": "^18.2.0", "react-dom": "^18.2.0", "tailwindcss": "^3.1.0", "vite": "^6.0.11"}, "dependencies": {"lucide-react": "^0.475.0", "next": "^15.2.1", "react-router-dom": "^7.1.5"}}