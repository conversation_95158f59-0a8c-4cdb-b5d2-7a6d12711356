import {Product, ChatMessage} from '../types';

// Base API URL - you can adjust this as needed
const API_URL = 'https://shop.citisolar.co.zw/api';

//const API_URL = 'http://localhost:8000/api';

interface FetchProductsParams {
    category?: string;
    minPrice?: number;
    maxPrice?: number;
    sortBy?: string;
    sortOrder?: 'asc' | 'desc';
    page?: number;
    perPage?: number;
}

export async function fetchProducts(params: FetchProductsParams = {}): Promise<{
    products: Product[];
    pagination: {
        currentPage: number;
        lastPage: number;
        total: number;
        perPage: number;
    };
}> {
    try {
        // Build query string from params
        const queryParams = new URLSearchParams();

        if (params.category) queryParams.append('category', params.category);
        if (params.minPrice !== undefined) queryParams.append('minPrice', params.minPrice.toString());
        if (params.maxPrice !== undefined) queryParams.append('maxPrice', params.maxPrice.toString());
        if (params.sortBy) queryParams.append('sortBy', params.sortBy);
        if (params.sortOrder) queryParams.append('sortOrder', params.sortOrder);
        if (params.page) queryParams.append('page', params.page.toString());
        if (params.perPage) queryParams.append('perPage', params.perPage.toString());
        else queryParams.append('perPage', '15'); // Default to 15 items per page

        const queryString = queryParams.toString();
        const url = `${API_URL}/products${queryString ? `?${queryString}` : ''}`;

        const response = await fetch(url);

        if (!response.ok) {
            throw new Error(`Failed to fetch products: ${response.status} ${response.statusText}`);
        }

        const responseData = await response.json();

        // Debug the response structure
        console.log('API Response:', responseData);

        // The Laravel API returns a paginated response with 'data' property
        // containing the actual products
        if (!responseData.data) {
            console.error('Unexpected API response structure:', responseData);
            return {products: [], pagination: {currentPage: 1, lastPage: 1, total: 0, perPage: 15}};
        }

        // Map the response data to match the Product interface
        const products = responseData.data.map((item: any): Product => ({
            id: item.id.toString(),
            name: item.name,
            description: item.description || '',
            price: parseFloat(item.price),
            category: item.category?.name || '',
            categoryId: item.category_id || item.category?.id || '',
            image: item.image_url || '',
            brand: item.brand || '',
            specifications: item.specifications || {},
            slug: item.slug || item.id.toString(), // Use slug if available, fallback to ID
        }));

        // Extract pagination information
        // Check if the response has the expected pagination meta
        let pagination;
        if (responseData.meta) {
            pagination = {
                currentPage: responseData.meta.current_page,
                lastPage: responseData.meta.last_page,
                total: responseData.meta.total,
                perPage: responseData.meta.per_page
            };
        } else {
            // Fallback for APIs that don't return meta field
            // This may happen if the backend doesn't use Laravel's paginate()
            // or if the format is different
            console.warn('API response missing pagination metadata, using fallback pagination');
            pagination = {
                currentPage: params.page || 1,
                lastPage: 1,
                total: products.length,
                perPage: params.perPage || 15
            };
        }

        return {products, pagination};
    } catch (error) {
        console.error('Error fetching products:', error);
        throw error;
    }
}

// This is a helper function in case you need to directly get categories without the products
export async function fetchCategories() {
    try {
        const response = await fetch(`${API_URL}/categories`);

        if (!response.ok) {
            throw new Error(`Failed to fetch categories: ${response.status} ${response.statusText}`);
        }

        const data = await response.json();

        // Return the categories array directly
        // The Laravel API should return an array of categories
        if (!Array.isArray(data)) {
            console.warn('Unexpected categories response format:', data);
            return [];
        }

        return data;
    } catch (error) {
        console.error('Error fetching categories:', error);
        throw error;
    }
}

export async function fetchChatMessages(productId: string): Promise<ChatMessage[]> {
    try {
        const response = await fetch(`${API_URL}/chat/messages?productId=${productId}`);

        if (!response.ok) {
            throw new Error(`Failed to fetch chat messages: ${response.status} ${response.statusText}`);
        }

        const data = await response.json();

        return data.messages.map((msg: any): ChatMessage => ({
            id: msg.id,
            productId,
            content: msg.content,
            timestamp: new Date(msg.created_at),
            isAgent: msg.is_agent,
            userName: msg.is_agent ? 'Agent' : 'You'
        }));
    } catch (error) {
        console.error('Error fetching chat messages:', error);
        throw error;
    }
}

export async function sendChatMessage(
    sessionId: string,
    content: string,
    isAgent: boolean
): Promise<ChatMessage> {
    try {
        const response = await fetch(`${API_URL}/chat/messages`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                session_id: sessionId,
                content,
                is_agent: isAgent
            }),
        });

        if (!response.ok) {
            throw new Error(`Failed to send chat message: ${response.status} ${response.statusText}`);
        }

        const data = await response.json();

        return {
            id: data.id,
            productId: data.product_id || sessionId,
            content: data.content,
            timestamp: new Date(data.created_at),
            isAgent: data.is_agent,
            userName: data.is_agent ? 'Agent' : 'You'
        };
    } catch (error) {
        console.error('Error sending chat message:', error);
        throw error;
    }
}

export async function fetchPackages() {
    try {
        // Since packages might be part of the same API endpoint
        const response = await fetch(`${API_URL}/products`);

        if (!response.ok) {
            throw new Error(`Failed to fetch packages: ${response.status} ${response.statusText}`);
        }

        const responseData = await response.json();

        // If there's a dedicated packages endpoint in your API, you can use this instead
        // If not, we'll use the mock data since it's not in the example response
        const mockPackages = [
            {
                "id": "pkg-001",
                "name": "Basic Home Solar",
                "description": "Perfect for small homes with basic power needs",
                "price": 1499.99,
                "features": [
                    "2kW Solar System",
                    "4x Solar Panels",
                    "1x Inverter",
                    "2x Batteries",
                    "Basic Installation",
                    "2-Year Warranty"
                ]
            },
            {
                "id": "pkg-002",
                "name": "Standard Power",
                "description": "Ideal for medium-sized homes with moderate usage",
                "price": 2499.99,
                "features": [
                    "4kW Solar System",
                    "8x Solar Panels",
                    "1x Premium Inverter",
                    "4x Batteries",
                    "Professional Installation",
                    "5-Year Warranty"
                ]
            },
            {
                "id": "pkg-003",
                "name": "Premium Power",
                "description": "Complete solution for large homes with high power demands",
                "price": 3999.99,
                "features": [
                    "8kW Solar System",
                    "16x Solar Panels",
                    "2x Premium Inverters",
                    "8x High-Capacity Batteries",
                    "Premium Installation",
                    "10-Year Warranty"
                ]
            }
        ];

        // If your API has packages in a different property, modify this accordingly
        return responseData.packages || mockPackages;
    } catch (error) {
        console.error('Error fetching packages:', error);

        // Fallback to mock data if API fails
        return [
            {
                "id": "pkg-001",
                "name": "Basic Home Solar",
                "description": "Perfect for small homes with basic power needs",
                "price": 1499.99,
                "features": [
                    "2kW Solar System",
                    "4x Solar Panels",
                    "1x Inverter",
                    "2x Batteries",
                    "Basic Installation",
                    "2-Year Warranty"
                ]
            },
            {
                "id": "pkg-002",
                "name": "Standard Power",
                "description": "Ideal for medium-sized homes with moderate usage",
                "price": 2499.99,
                "features": [
                    "4kW Solar System",
                    "8x Solar Panels",
                    "1x Premium Inverter",
                    "4x Batteries",
                    "Professional Installation",
                    "5-Year Warranty"
                ]
            },
            {
                "id": "pkg-003",
                "name": "Premium Power",
                "description": "Complete solution for large homes with high power demands",
                "price": 3999.99,
                "features": [
                    "8kW Solar System",
                    "16x Solar Panels",
                    "2x Premium Inverters",
                    "8x High-Capacity Batteries",
                    "Premium Installation",
                    "10-Year Warranty"
                ]
            }
        ];
    }
}

export async function fetchAddons() {
    try {
        // Since addons might be part of the same API endpoint
        const response = await fetch(`${API_URL}/products`);

        if (!response.ok) {
            throw new Error(`Failed to fetch addons: ${response.status} ${response.statusText}`);
        }

        const responseData = await response.json();

        // If there's a dedicated addons endpoint in your API, you can use this instead
        // If not, we'll use mock data since it's not in the example response
        const mockAddons = [
            {
                "id": "addon-001",
                "name": "Extra Battery",
                "price": 599.99,
                "description": "Additional battery for extended power storage"
            },
            {
                "id": "addon-002",
                "name": "Extra Solar Panel",
                "price": 299.99,
                "description": "Additional solar panel for increased power generation"
            },
            {
                "id": "addon-003",
                "name": "Smart Monitoring",
                "price": 199.99,
                "description": "24/7 system monitoring and mobile app access"
            },
            {
                "id": "addon-004",
                "name": "Annual Maintenance",
                "price": 299.99,
                "description": "Yearly maintenance and system check-up"
            }
        ];

        // If your API has addons in a different property, modify this accordingly
        return responseData.addons || mockAddons;
    } catch (error) {
        console.error('Error fetching addons:', error);

        // Fallback to mock data if API fails
        return [
            {
                "id": "addon-001",
                "name": "Extra Battery",
                "price": 599.99,
                "description": "Additional battery for extended power storage"
            },
            {
                "id": "addon-002",
                "name": "Extra Solar Panel",
                "price": 299.99,
                "description": "Additional solar panel for increased power generation"
            },
            {
                "id": "addon-003",
                "name": "Smart Monitoring",
                "price": 199.99,
                "description": "24/7 system monitoring and mobile app access"
            },
            {
                "id": "addon-004",
                "name": "Annual Maintenance",
                "price": 299.99,
                "description": "Yearly maintenance and system check-up"
            }
        ];
    }
}

// Create a new order
export interface OrderRequest {
    items: {
        product: {
            id: string;
            price: number;
        };
        quantity: number;
    }[];
    shipping: {
        firstName: string;
        lastName: string;
        email: string;
        phone: string;
        address: string;
        city: string;
        country: string;
        zipCode?: string;
    };
    payment_method: string;
    total: number;
    subtotal: number;
    tax: number;
    shipping_fee: number;
}

export interface OrderResponse {
    success: boolean;
    id: string;
    order_number: string;
    message: string;
}

export async function createOrder(orderData: OrderRequest): Promise<OrderResponse> {
    try {
        const response = await fetch(`${API_URL}/orders`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(orderData),
        });

        if (!response.ok) {
            const errorText = await response.text();
            console.error('Order creation failed:', errorText);
            throw new Error('Failed to create order');
        }

        return await response.json();
    } catch (error) {
        console.error('Error creating order:', error);
        throw error;
    }
}

// Get order by ID
export interface Order {
    id: string;
    order_number: string;
    first_name: string;
    last_name: string;
    email: string;
    phone: string;
    address: string;
    city: string;
    country: string;
    zip_code?: string;
    payment_method: string;
    subtotal: number;
    tax: number;
    shipping_fee: number;
    total: number;
    status: 'pending' | 'processing' | 'completed' | 'cancelled' | 'refunded';
    created_at: string;
    updated_at: string;
    items: {
        id: string;
        product_id: string;
        quantity: number;
        price: number;
        subtotal: number;
        product: Product;
    }[];
}

export async function getOrder(orderId: string): Promise<Order> {
    try {
        const response = await fetch(`${API_URL}/orders/${orderId}`);

        if (!response.ok) {
            const errorText = await response.text();
            console.error('Order retrieval failed:', errorText);
            throw new Error('Failed to retrieve order');
        }

        const responseData = await response.json();

        if (!responseData.success) {
            throw new Error(responseData.message || 'Failed to retrieve order');
        }

        return responseData.order;
    } catch (error) {
        console.error('Error getting order:', error);
        throw error;
    }
}

// Initiate a payment with PayNow
export interface PaymentRequest {
    order_id: string;
    amount: number;
    email: string;
    phone: string;
    merchant_reference: string;
}

export interface PaymentResponse {
    success: boolean;
    payment_id: string;
    redirect_url?: string;
    message: string;
}

export async function initiatePaynowPayment(paymentData: PaymentRequest): Promise<PaymentResponse> {
    try {
        const response = await fetch(`${API_URL}/payments/paynow/initiate`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(paymentData),
        });

        if (!response.ok) {
            const errorText = await response.text();
            console.error('Payment initiation failed:', errorText);
            throw new Error('Failed to initiate payment');
        }

        return await response.json();
    } catch (error) {
        console.error('Error initiating payment:', error);
        throw error;
    }
}

// Check payment status
export interface PaymentStatus {
    status: 'pending' | 'paid' | 'failed' | 'refunded';
    payment_id: string;
    paid_at?: string;
}

export async function checkPaymentStatus(paymentId: string): Promise<PaymentStatus> {
    try {
        const response = await fetch(`${API_URL}/payment/status?payment_id=${paymentId}`);

        if (!response.ok) {
            const errorText = await response.text();
            console.error('Payment status check failed:', errorText);
            throw new Error('Failed to check payment status');
        }

        return await response.json();
    } catch (error) {
        console.error('Error checking payment status:', error);
        throw error;
    }
}
