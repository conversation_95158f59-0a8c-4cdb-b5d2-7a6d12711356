<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>Citi Solar</title>
    <link rel="icon" href="/favicon.ico" />
    
    @php
    // Check if we're running the development server
    $devServerRunning = false;
    $handle = @fsockopen('localhost', 5174, $errno, $errstr, 1); 
    if ($handle) {
        $devServerRunning = true;
        fclose($handle);
    }
    
    // Check if we have built assets
    $manifestPath = public_path('build/.vite/manifest.json');
    $hasManifest = file_exists($manifestPath);
    @endphp
    
    @if($devServerRunning)
        <!-- Development mode - connect to dev server -->
        <script type="module" src="http://localhost:5174/@vite/client"></script>
        <script type="module" src="http://localhost:5174/resources/js/React/src/main.tsx"></script>
    @elseif($hasManifest)
        <!-- Production mode - use built assets -->
        @php
        $manifest = json_decode(file_get_contents($manifestPath), true);
        $cssFiles = $manifest['resources/js/React/src/main.tsx']['css'] ?? [];
        $jsFile = $manifest['resources/js/React/src/main.tsx']['file'] ?? null;
        @endphp
        
        @foreach($cssFiles as $cssFile)
            <link rel="stylesheet" href="{{ asset('build/' . $cssFile) }}">
        @endforeach
        
        @if($jsFile)
            <script type="module" src="{{ asset('build/' . $jsFile) }}"></script>
        @endif
    @else
        <!-- Fallback for when assets aren't built yet -->
        <div style="font-family: system-ui; padding: 2rem; text-align: center;">
            <h1>Assets Not Found</h1>
            <p>Please run <code>npm run build</code> to generate the assets or start the development server with <code>npm run dev</code>.</p>
        </div>
    @endif
</head>
<body>
    <div id="react-root"></div>
</body>
</html>