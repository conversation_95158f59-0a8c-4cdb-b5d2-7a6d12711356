import { defineConfig, loadEnv } from 'vite';
import laravel from 'laravel-vite-plugin';
import react from '@vitejs/plugin-react';

export default defineConfig(({ mode }) => {
    // Load environment variables from .env files
    const env = loadEnv(mode, process.cwd());

    return {
        plugins: [
            laravel({
                input: ['resources/js/React/src/index.css', 'resources/js/React/src/main.tsx'],
                refresh: true,
            }),
            react({
                // Explicitly configure JSX runtime
                jsxRuntime: 'automatic',
                // Include TypeScript files
                include: ['**/*.tsx', '**/*.ts', '**/*.jsx', '**/*.js'],
                // Exclude node_modules
                exclude: /node_modules/,
            }),
        ],
        resolve: {
            alias: {
                '@': '/resources/js/React/src',
            },
        },
        build: {
            outDir: 'public/build',
            emptyOutDir: true,
            manifest: true,
        },
        // Make env variables available to the client-side code
        define: {
            'process.env.VITE_PUSHER_APP_KEY': JSON.stringify(env.PUSHER_APP_KEY || env.VITE_PUSHER_APP_KEY),
            'process.env.VITE_PUSHER_APP_CLUSTER': JSON.stringify(env.PUSHER_APP_CLUSTER || env.VITE_PUSHER_APP_CLUSTER),
        },
        // Ensure proper file extensions are handled
        esbuild: {
            jsx: 'automatic',
        },
    };
});
