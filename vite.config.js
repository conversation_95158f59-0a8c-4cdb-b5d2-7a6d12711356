import { defineConfig, loadEnv } from 'vite';
import laravel from 'laravel-vite-plugin';
import react from '@vitejs/plugin-react';

export default defineConfig(({ mode }) => {
    // Load environment variables from .env files
    const env = loadEnv(mode, process.cwd());
    
    // List of environment variables to expose to the client
    const envPrefix = [
        'VITE_',
        'PUSHER_APP_KEY',
        'PUSHER_APP_CLUSTER',
        'PUSHER_APP_HOST',
        'PUSHER_APP_PORT',
    ];
    
    return {
        plugins: [
            laravel({
                input: ['resources/js/React/src/index.css', 'resources/js/React/src/main.tsx'],
                refresh: true,
            }),
            react(),
        ],
        resolve: {
            alias: {
                '@': '/resources/js/React/src',
            },
        },
        build: {
            outDir: 'public/build',
            emptyOutDir: true,
            manifest: true,
        },
        // Make env variables available to the client-side code
        define: {
            'process.env.VITE_PUSHER_APP_KEY': JSON.stringify(env.PUSHER_APP_KEY || env.VITE_PUSHER_APP_KEY),
            'process.env.VITE_PUSHER_APP_CLUSTER': JSON.stringify(env.PUSHER_APP_CLUSTER || env.VITE_PUSHER_APP_CLUSTER),
        },
    };
});
